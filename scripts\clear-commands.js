const { REST, Routes } = require('discord.js');
require('dotenv').config();

// Construct and prepare an instance of the REST module
const rest = new REST().setToken(process.env.DISCORD_TOKEN);

// Clear commands
(async () => {
    try {
        console.log('Started clearing application (/) commands...');

        // Clear both guild and global commands to avoid conflicts
        if (process.env.GUILD_ID) {
            // Clear guild-specific commands
            await rest.put(
                Routes.applicationGuildCommands(process.env.CLIENT_ID, process.env.GUILD_ID),
                { body: [] },
            );
            console.log(`Successfully cleared all guild commands for guild ${process.env.GUILD_ID}.`);
        }

        // Also clear global commands to prevent conflicts
        await rest.put(
            Routes.applicationCommands(process.env.CLIENT_ID),
            { body: [] },
        );
        console.log('Successfully cleared all global commands.');

        console.log('✅ All commands have been cleared!');
        console.log('💡 Now run "npm run deploy" to register commands again.');
    } catch (error) {
        console.error('Error clearing commands:', error);
    }
})();
