const { REST, Routes } = require('discord.js');
require('dotenv').config();

// Construct and prepare an instance of the REST module
const rest = new REST().setToken(process.env.DISCORD_TOKEN);

// Clear commands
(async () => {
    try {
        console.log('Started clearing application (/) commands...');

        if (process.env.GUILD_ID) {
            // Clear guild-specific commands
            await rest.put(
                Routes.applicationGuildCommands(process.env.CLIENT_ID, process.env.GUILD_ID),
                { body: [] },
            );
            console.log(`Successfully cleared all guild commands for guild ${process.env.GUILD_ID}.`);
        } else {
            console.log('No GUILD_ID found, clearing global commands...');
            // Clear global commands
            await rest.put(
                Routes.applicationCommands(process.env.CLIENT_ID),
                { body: [] },
            );
            console.log('Successfully cleared all global commands.');
        }

        console.log('✅ All commands have been cleared!');
    } catch (error) {
        console.error('Error clearing commands:', error);
    }
})();
