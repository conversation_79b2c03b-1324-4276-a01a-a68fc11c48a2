const { EmbedBuilder } = require('discord.js');

class CustomEmbedBuilder {
    static createEmbed(type, client, options = {}) {
        const embed = new EmbedBuilder();
        
        // Set timestamp by default
        embed.setTimestamp();
        
        switch (type) {
            case 'success':
                embed.setColor('#00FF7F')
                     .setAuthor({ 
                         name: '✅ ACTION SUCCESS', 
                         iconURL: client.user.displayAvatarURL() 
                     });
                break;
                
            case 'error':
                embed.setColor('#C48AEB')
                     .setAuthor({ 
                         name: '🚫 ACTION FAILED', 
                         iconURL: client.user.displayAvatarURL() 
                     });
                break;
                
            case 'warning':
                embed.setColor('#FFA500')
                     .setAuthor({ 
                         name: '⚠️ THÔNG BÁO', 
                         iconURL: client.user.displayAvatarURL() 
                     });
                break;
                
            case 'info':
                embed.setColor('#00BFFF')
                     .setAuthor({ 
                         name: 'ℹ️ THÔNG TIN', 
                         iconURL: client.user.displayAvatarURL() 
                     });
                break;
                
            case 'loading':
                embed.setColor('#FFD700')
                     .setAuthor({ 
                         name: '⏳ ĐANG XỬ LÝ', 
                         iconURL: client.user.displayAvatarURL() 
                     });
                break;
                
            default:
                embed.setColor('#36393F')
                     .setAuthor({ 
                         name: '📋 BOOKING BOT', 
                         iconURL: client.user.displayAvatarURL() 
                     });
        }
        
        // Apply additional options
        if (options.description) embed.setDescription(options.description);
        if (options.fields) embed.addFields(options.fields);
        if (options.footer) embed.setFooter(options.footer);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.title) {
            // Override author if title is provided
            embed.setAuthor(null).setTitle(options.title);
        }
        
        return embed;
    }
    
    // Quick methods for common embed types
    static success(client, description, options = {}) {
        return this.createEmbed('success', client, { description, ...options });
    }
    
    static error(client, description, options = {}) {
        return this.createEmbed('error', client, { description, ...options });
    }
    
    static warning(client, description, options = {}) {
        return this.createEmbed('warning', client, { description, ...options });
    }
    
    static info(client, description, options = {}) {
        return this.createEmbed('info', client, { description, ...options });
    }
    
    static loading(client, description, options = {}) {
        return this.createEmbed('loading', client, { description, ...options });
    }
    
    // Booking specific embeds
    static bookingSuccess(client, bookingData) {
        return this.createEmbed('success', client, {
            description: `Booking đã được tạo thành công!`,
            fields: [
                { name: '🆔 ID Booking', value: `#${bookingData.id}`, inline: true },
                { name: '⏰ Thời gian', value: `${bookingData.hours} giờ`, inline: true },
                { name: '💰 Tổng tiền', value: `${bookingData.total_price.toLocaleString()} VND`, inline: true },
                { name: '📝 Mô tả', value: bookingData.description || 'Không có mô tả', inline: false }
            ]
        });
    }
    
    static bookingError(client, reason) {
        return this.createEmbed('error', client, {
            description: `Không thể tạo booking: ${reason}`
        });
    }
}

module.exports = CustomEmbedBuilder;
