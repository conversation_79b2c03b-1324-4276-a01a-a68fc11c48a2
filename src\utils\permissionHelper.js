const roleManager = require('./roleManager');

class PermissionHelper {
    /**
     * Check if user has required permission level
     * @param {GuildMember} member - Discord guild member
     * @param {string} requiredLevel - Required permission level: 'owner', 'admin', 'support'
     * @returns {Promise<boolean>}
     */
    static async hasPermission(member, requiredLevel) {
        return await roleManager.checkPermission(member, requiredLevel);
    }

    /**
     * Get user's permission level
     * @param {GuildMember} member - Discord guild member
     * @returns {Promise<string>} - 'bot_owner', 'owner', 'admin', 'support', 'user'
     */
    static async getUserLevel(member) {
        return await roleManager.getUserLevel(member);
    }

    /**
     * Create permission check middleware for commands
     * @param {string} requiredLevel - Required permission level
     * @returns {Function}
     */
    static requirePermission(requiredLevel) {
        return async (interaction) => {
            const hasPermission = await this.hasPermission(interaction.member, requiredLevel);
            
            if (!hasPermission) {
                const levelNames = {
                    owner: 'Owner',
                    admin: 'Admin',
                    support: 'Support'
                };
                
                await interaction.reply({
                    content: `<:error:1400542723014000731> | Bạn cần quyền **${levelNames[requiredLevel]}** để sử dụng lệnh này!`,
                    ephemeral: true
                });
                return false;
            }
            
            return true;
        };
    }

    /**
     * Get permission level display name
     * @param {string} level - Permission level
     * @returns {string}
     */
    static getLevelDisplayName(level) {
        const names = {
            bot_owner: '🔧 Bot Owner',
            owner: '👑 Owner',
            admin: '⚡ Admin',
            support: '🛠️ Support',
            user: '👤 User'
        };
        
        return names[level] || '❓ Unknown';
    }

    /**
     * Check if user can manage another user (based on role hierarchy)
     * @param {GuildMember} executor - User executing the action
     * @param {GuildMember} target - Target user
     * @returns {Promise<boolean>}
     */
    static async canManageUser(executor, target) {
        const executorLevel = await this.getUserLevel(executor);
        const targetLevel = await this.getUserLevel(target);
        
        const hierarchy = {
            bot_owner: 5,
            owner: 4,
            admin: 3,
            support: 2,
            user: 1
        };
        
        return hierarchy[executorLevel] > hierarchy[targetLevel];
    }
}

module.exports = PermissionHelper;
