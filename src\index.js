const { Client, GatewayIntentBits, Collection } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('./config/config');
const database = require('./database/database');
const prefixManager = require('./utils/prefixManager');

// Validate configuration
try {
    config.validate();
} catch (error) {
    console.error('Configuration error:', error.message);
    process.exit(1);
}

// Create client
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
    ],
});

// Collections for commands
client.commands = new Collection();
client.slashCommands = new Collection();

// Load slash commands
const slashCommandsPath = path.join(__dirname, 'commands', 'slash');
if (fs.existsSync(slashCommandsPath)) {
    const slashCommandFiles = fs.readdirSync(slashCommandsPath).filter(file => file.endsWith('.js'));

    for (const file of slashCommandFiles) {
        const filePath = path.join(slashCommandsPath, file);
        const command = require(filePath);
        
        if ('data' in command && 'execute' in command) {
            client.slashCommands.set(command.data.name, command);
            console.log(`Loaded slash command: ${command.data.name}`);
        } else {
            console.log(`[WARNING] The slash command at ${filePath} is missing a required "data" or "execute" property.`);
        }
    }
}

// Load prefix commands (for future use)
const prefixCommandsPath = path.join(__dirname, 'commands', 'prefix');
if (fs.existsSync(prefixCommandsPath)) {
    const prefixCommandFiles = fs.readdirSync(prefixCommandsPath).filter(file => file.endsWith('.js'));

    for (const file of prefixCommandFiles) {
        const filePath = path.join(prefixCommandsPath, file);
        const command = require(filePath);
        
        if ('name' in command && 'execute' in command) {
            client.commands.set(command.name, command);
            console.log(`Loaded prefix command: ${command.name}`);
        } else {
            console.log(`[WARNING] The prefix command at ${filePath} is missing a required "name" or "execute" property.`);
        }
    }
}

// Event: Bot ready
client.once('ready', async () => {
    console.log(`✅ Bot is ready! Logged in as ${client.user.tag}`);
    
    // Initialize database
    try {
        await database.initialize();
    } catch (error) {
        console.error('Failed to initialize database:', error);
        process.exit(1);
    }
});

// Event: Slash command interaction
client.on('interactionCreate', async interaction => {
    if (!interaction.isChatInputCommand()) return;

    const command = client.slashCommands.get(interaction.commandName);

    if (!command) {
        console.error(`No slash command matching ${interaction.commandName} was found.`);
        return;
    }

    try {
        await command.execute(interaction);
    } catch (error) {
        console.error('Error executing slash command:', error);
        
        const errorMessage = { 
            content: 'Đã xảy ra lỗi khi thực hiện lệnh!', 
            ephemeral: true 
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
});

// Event: Message for prefix commands (basic setup for future)
client.on('messageCreate', async message => {
    if (message.author.bot) return;

    const prefix = await prefixManager.getPrefix(message.guild?.id);
    
    if (!message.content.startsWith(prefix)) return;

    const args = message.content.slice(prefix.length).trim().split(/ +/);
    const commandName = args.shift().toLowerCase();

    const command = client.commands.get(commandName);

    if (!command) return;

    try {
        await command.execute(message, args);
    } catch (error) {
        console.error('Error executing prefix command:', error);
        message.reply('Đã xảy ra lỗi khi thực hiện lệnh!');
    }
});

// Error handling
process.on('unhandledRejection', error => {
    console.error('Unhandled promise rejection:', error);
});

process.on('uncaughtException', error => {
    console.error('Uncaught exception:', error);
    process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down bot...');
    await database.close();
    client.destroy();
    process.exit(0);
});

// Login
client.login(config.discord.token);
