const db = require('../database/database');
const config = require('../config/config');

class PrefixManager {
    constructor() {
        this.cache = new Map(); // Cache prefixes in memory for better performance
    }

    async getPrefix(guildId) {
        // Check cache first
        if (this.cache.has(guildId)) {
            return this.cache.get(guildId);
        }

        // If no guild ID (DM), return default prefix
        if (!guildId) {
            return config.discord.defaultPrefix;
        }

        try {
            // Get from database
            const result = await db.get(
                'SELECT prefix FROM guild_settings WHERE guild_id = ?',
                [guildId]
            );

            const prefix = result ? result.prefix : config.discord.defaultPrefix;
            
            // Cache the result
            this.cache.set(guildId, prefix);
            
            return prefix;
        } catch (error) {
            console.error('Error getting prefix:', error);
            return config.discord.defaultPrefix;
        }
    }

    async setPrefix(guildId, newPrefix) {
        if (!guildId) {
            throw new Error('Cannot set prefix for DM channels');
        }

        // Validate prefix
        if (!this.isValidPrefix(newPrefix)) {
            throw new Error('Invalid prefix. Prefix must be 1-3 characters long and cannot contain spaces or @');
        }

        try {
            // Update or insert into database
            await db.run(
                `INSERT OR REPLACE INTO guild_settings (guild_id, prefix, updated_at) 
                 VALUES (?, ?, CURRENT_TIMESTAMP)`,
                [guildId, newPrefix]
            );

            // Update cache
            this.cache.set(guildId, newPrefix);

            return true;
        } catch (error) {
            console.error('Error setting prefix:', error);
            throw error;
        }
    }

    isValidPrefix(prefix) {
        // Check if prefix is valid
        if (!prefix || typeof prefix !== 'string') return false;
        if (prefix.length < 1 || prefix.length > 3) return false;
        if (prefix.includes(' ') || prefix.includes('@')) return false;
        
        return true;
    }

    // Clear cache for a specific guild (useful when bot leaves a server)
    clearCache(guildId) {
        this.cache.delete(guildId);
    }

    // Clear entire cache
    clearAllCache() {
        this.cache.clear();
    }
}

module.exports = new PrefixManager();
