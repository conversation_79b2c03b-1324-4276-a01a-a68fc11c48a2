const { SlashCommandBuilder, PermissionFlagsBits, ChannelType } = require('discord.js');
const config = require('../../config/config');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('create')
        .setDescription('Tạo các kênh log cho bot')
        .addSubcommand(subcommand =>
            subcommand
                .setName('logs')
                .setDescription('Tạo các kênh log')
                .addBooleanOption(option =>
                    option.setName('create_all')
                        .setDescription('Tạo tất cả kênh log (True) hay chỉ một số kênh (False)')
                        .setRequired(true)
                )
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageChannels),

    async execute(interaction) {
        try {
            const subcommand = interaction.options.getSubcommand();
            const guildId = interaction.guild?.id;

            // Check if command is used in DM
            if (!guildId) {
                return await interaction.reply({ 
                    content: '<:error:1400542723014000731> | <PERSON>ệnh này chỉ có thể sử dụng trong server!', 
                    ephemeral: true 
                });
            }

            // Check permissions
            const member = interaction.member;
            const isOwner = member.id === config.discord.ownerId;
            const hasManageChannels = member.permissions.has(PermissionFlagsBits.ManageChannels);

            if (!isOwner && !hasManageChannels) {
                return await interaction.reply({ 
                    content: '<:error:1400542723014000731> | Bạn cần quyền **Manage Channels** để sử dụng lệnh này!', 
                    ephemeral: true 
                });
            }

            // Check if bot has permissions
            const botMember = interaction.guild.members.me;
            if (!botMember.permissions.has(PermissionFlagsBits.ManageChannels)) {
                return await interaction.reply({ 
                    content: '<:error:1400542723014000731> | Bot cần quyền **Manage Channels** để tạo kênh!', 
                    ephemeral: true 
                });
            }

            if (subcommand === 'logs') {
                await handleLogsCreation(interaction);
            }

        } catch (error) {
            console.error('Error in create command:', error);

            const errorMessage = { 
                content: '<:error:1400542723014000731> | Đã xảy ra lỗi khi tạo kênh. Vui lòng thử lại sau!', 
                ephemeral: true 
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    },
};



async function handleLogsCreation(interaction) {
    const createAll = interaction.options.getBoolean('create_all');
    
    await interaction.deferReply({ ephemeral: true });

    try {
        const categoryId = '1398225656109600818';
        let category = interaction.guild.channels.cache.get(categoryId);

        // If category doesn't exist, create it
        if (!category) {
            try {
                category = await interaction.guild.channels.create({
                    name: 'LOG CHANNELS',
                    type: ChannelType.GuildCategory,
                    permissionOverwrites: [
                        {
                            id: interaction.guild.roles.everyone,
                            deny: [PermissionFlagsBits.ViewChannel],
                        },
                        {
                            id: interaction.guild.members.me.id,
                            allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.SendMessages],
                        },
                    ],
                });

                await interaction.editReply({
                    content: `<:success:1400542723014000731> | Đã tạo danh mục: ${category.name}\n⏳ Đang tạo các kênh log...`
                });

                // Small delay after creating category
                await new Promise(resolve => setTimeout(resolve, 1000));

            } catch (error) {
                console.error('Error creating category:', error);
                return await interaction.editReply({
                    content: '<:error:1400542723014000731> | Lỗi khi tạo danh mục log!'
                });
            }
        }

        const logChannels = [
            { name: 'commands-log', topic: 'Log các lệnh được sử dụng' },
            { name: 'reset-log', topic: 'Log các lệnh reset' },
            { name: 'cash-log', topic: 'Log giao dịch tiền tệ' },
            { name: 'shop-log', topic: 'Log mua bán trong shop' },
            { name: 'star-log', topic: 'Log hệ thống sao/điểm' },
            { name: 'box-log', topic: 'Log mở hộp/gacha' },
            { name: 'ticket-log', topic: 'Log hệ thống ticket' }
        ];

        let channelsToCreate = logChannels;
        
        if (!createAll) {
            // If not creating all, create only essential ones
            channelsToCreate = [
                { name: 'commands-log', topic: 'Log các lệnh được sử dụng' },
                { name: 'cash-log', topic: 'Log giao dịch tiền tệ' },
                { name: 'ticket-log', topic: 'Log hệ thống ticket' }
            ];
        }

        const createdChannels = [];
        const errors = [];

        for (const channelInfo of channelsToCreate) {
            try {
                // Check if channel already exists
                const existingChannel = interaction.guild.channels.cache.find(
                    ch => ch.name === channelInfo.name && ch.parentId === categoryId
                );

                if (existingChannel) {
                    continue; // Skip if already exists
                }

                const channel = await interaction.guild.channels.create({
                    name: channelInfo.name,
                    type: ChannelType.GuildText,
                    parent: categoryId,
                    topic: channelInfo.topic,
                    permissionOverwrites: [
                        {
                            id: interaction.guild.roles.everyone,
                            deny: [PermissionFlagsBits.ViewChannel],
                        },
                        {
                            id: interaction.guild.members.me.id,
                            allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.SendMessages],
                        },
                    ],
                });

                createdChannels.push(channel.name);
                
                // Small delay to avoid rate limits
                await new Promise(resolve => setTimeout(resolve, 500));
                
            } catch (error) {
                console.error(`Error creating channel ${channelInfo.name}:`, error);
                errors.push(channelInfo.name);
            }
        }

        let responseMessage = '';
        
        if (createdChannels.length > 0) {
            responseMessage += `<:done:1400542641958944928> | Đã tạo ${createdChannels.length} kênh log:\n`;
            responseMessage += createdChannels.map(name => `• ${name}`).join('\n');
        }
        
        if (errors.length > 0) {
            responseMessage += `\n\n<:error:1400542723014000731> | Lỗi khi tạo ${errors.length} kênh:\n`;
            responseMessage += errors.map(name => `• ${name}`).join('\n');
        }
        
        if (createdChannels.length === 0 && errors.length === 0) {
            responseMessage = 'Tất cả kênh đã tồn tại!';
        }

        await interaction.editReply({ content: responseMessage });

    } catch (error) {
        console.error('Error creating log channels:', error);
        await interaction.editReply({ 
            content: '<:error:1400542723014000731> | Lỗi khi tạo kênh log!' 
        });
    }
}
