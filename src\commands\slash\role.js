const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const roleManager = require('../../utils/roleManager');
const config = require('../../config/config');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('role')
        .setDescription('Cài đặt role phân quyền cho bot')
        .addSubcommand(subcommand =>
            subcommand
                .setName('owner')
                .setDescription('Cài đặt role Owner')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role Owner (quyền cao nhất)')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('admin')
                .setDescription('Cài đặt role Admin')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role Admin (quyền quản trị)')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('support')
                .setDescription('Cài đặt role Support')
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Role Support (quyền hỗ trợ)')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('view')
                .setDescription('Xem cài đặt role hiện tại')
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

    async execute(interaction) {
        try {
            const subcommand = interaction.options.getSubcommand();
            const guildId = interaction.guild?.id;

            // Check if command is used in DM
            if (!guildId) {
                return await interaction.reply({ 
                    content: '<:error:1400542723014000731> | Lệnh này chỉ có thể sử dụng trong server!', 
                    ephemeral: true 
                });
            }

            // Check permissions
            const member = interaction.member;
            const isOwner = member.id === config.discord.ownerId;
            const hasManageRoles = member.permissions.has(PermissionFlagsBits.ManageRoles);

            if (!isOwner && !hasManageRoles) {
                return await interaction.reply({ 
                    content: '<:error:1400542723014000731> | Bạn cần quyền **Manage Roles** để sử dụng lệnh này!', 
                    ephemeral: true 
                });
            }

            if (subcommand === 'view') {
                await handleViewRoles(interaction);
            } else {
                await handleSetRole(interaction, subcommand);
            }

        } catch (error) {
            console.error('Error in role command:', error);

            const errorMessage = { 
                content: '<:error:1400542723014000731> | Đã xảy ra lỗi khi cài đặt role. Vui lòng thử lại sau!', 
                ephemeral: true 
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    },
};

async function handleSetRole(interaction, roleType) {
    const role = interaction.options.getRole('role');
    const guildId = interaction.guild.id;

    try {
        // Validate role
        if (!role) {
            return await interaction.reply({ 
                content: '<:error:1400542723014000731> | Role không hợp lệ!', 
                ephemeral: true 
            });
        }

        // Check if role is @everyone
        if (role.id === interaction.guild.id) {
            return await interaction.reply({ 
                content: '<:error:1400542723014000731> | Không thể sử dụng role @everyone!', 
                ephemeral: true 
            });
        }

        // Set the role
        await roleManager.setRole(guildId, roleType, role.id);

        // Success response
        const roleTypeNames = {
            owner: 'Owner',
            admin: 'Admin', 
            support: 'Support'
        };

        await interaction.reply({ 
            content: `<:success:1400542723014000731> | Đã cài đặt role **${roleTypeNames[roleType]}**: ${role}\n📝 Role này sẽ có quyền ${roleType} trong các lệnh của bot`,
            ephemeral: true 
        });

        // Console log
        console.log(`Role ${roleType} set to ${role.name} (${role.id}) in guild ${interaction.guild.name} by ${interaction.user.tag}`);

    } catch (error) {
        console.error(`Error setting ${roleType} role:`, error);
        await interaction.reply({ 
            content: '<:error:1400542723014000731> | Lỗi khi cài đặt role!', 
            ephemeral: true 
        });
    }
}

async function handleViewRoles(interaction) {
    const guildId = interaction.guild.id;

    try {
        const roleSettings = await roleManager.getRoleSettings(guildId);
        
        let responseContent = '📋 **Cài đặt Role hiện tại:**\n\n';
        
        // Owner role
        if (roleSettings.owner) {
            const ownerRole = interaction.guild.roles.cache.get(roleSettings.owner);
            responseContent += `👑 **Owner**: ${ownerRole ? ownerRole.toString() : 'Role không tồn tại'}\n`;
        } else {
            responseContent += '👑 **Owner**: Chưa cài đặt\n';
        }
        
        // Admin role
        if (roleSettings.admin) {
            const adminRole = interaction.guild.roles.cache.get(roleSettings.admin);
            responseContent += `⚡ **Admin**: ${adminRole ? adminRole.toString() : 'Role không tồn tại'}\n`;
        } else {
            responseContent += '⚡ **Admin**: Chưa cài đặt\n';
        }
        
        // Support role
        if (roleSettings.support) {
            const supportRole = interaction.guild.roles.cache.get(roleSettings.support);
            responseContent += `🛠️ **Support**: ${supportRole ? supportRole.toString() : 'Role không tồn tại'}\n`;
        } else {
            responseContent += '🛠️ **Support**: Chưa cài đặt\n';
        }

        responseContent += '\n📝 **Phân cấp quyền:**\n';
        responseContent += '• Owner > Admin > Support > User\n';
        responseContent += '• Mỗi cấp cao hơn có tất cả quyền của cấp thấp hơn';

        await interaction.reply({ 
            content: responseContent,
            ephemeral: true 
        });

    } catch (error) {
        console.error('Error viewing roles:', error);
        await interaction.reply({ 
            content: '<:error:1400542723014000731> | Lỗi khi xem cài đặt role!', 
            ephemeral: true 
        });
    }
}
