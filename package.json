{"name": "discord-booking-bot", "version": "1.0.0", "description": "Discord bot for player booking service", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "setup": "node scripts/setup.js", "deploy": "node scripts/deploy-commands.js"}, "keywords": ["discord", "bot", "booking", "player", "service"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "sqlite3": "^5.1.6", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}