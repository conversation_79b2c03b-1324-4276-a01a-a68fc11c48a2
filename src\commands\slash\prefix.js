const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const prefixManager = require('../../utils/prefixManager');
const config = require('../../config/config');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('prefix')
        .setDescription('Thay đổi prefix của bot cho server này')
        .addStringOption(option =>
            option.setName('new_prefix')
                .setDescription('Prefix mới (1-3 ký tự, không chứa khoảng trắng hoặc @)')
                .setRequired(true)
                .setMaxLength(3)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),

    async execute(interaction) {
        try {
            const newPrefix = interaction.options.getString('new_prefix');
            const guildId = interaction.guild?.id;

            // Check if command is used in DM
            if (!guildId) {
                return await interaction.reply({
                    content: '<:error:1400542723014000731> | <PERSON><PERSON><PERSON> này chỉ có thể sử dụng trong server!',
                    ephemeral: true
                });
            }

            // Check permissions (additional check beyond default permissions)
            const member = interaction.member;
            const isOwner = member.id === config.discord.ownerId;
            const hasManageGuild = member.permissions.has(PermissionFlagsBits.ManageGuild);

            if (!isOwner && !hasManageGuild) {
                return await interaction.reply({
                    content: '<:error:1400542723014000731> | Bạn cần quyền **Manage Server** để sử dụng lệnh này!',
                    ephemeral: true
                });
            }

            // Get current prefix
            const currentPrefix = await prefixManager.getPrefix(guildId);

            // Check if new prefix is the same as current
            if (newPrefix === currentPrefix) {
                return await interaction.reply({
                    content: `Prefix hiện tại đã là \`${currentPrefix}\``,
                    ephemeral: true
                });
            }

            // Set new prefix
            await prefixManager.setPrefix(guildId, newPrefix);

            // Success response
            await interaction.reply({
                content: `<:done:1400542641958944928> | Prefix đã được thay đổi từ \`${currentPrefix}\` thành \`${newPrefix}\``,
                ephemeral: true
            });

            // Log the change
            console.log(`Prefix changed in guild ${interaction.guild.name} (${guildId}) from "${currentPrefix}" to "${newPrefix}" by ${interaction.user.tag}`);

        } catch (error) {
            console.error('Error in prefix command:', error);

            const errorMessage = {
                content: '<:error:1400542723014000731> | Đã xảy ra lỗi khi thay đổi prefix. Vui lòng thử lại sau!',
                ephemeral: true
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    },
};
