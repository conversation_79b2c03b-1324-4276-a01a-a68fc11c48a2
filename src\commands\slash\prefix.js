const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const prefixManager = require('../../utils/prefixManager');
const config = require('../../config/config');
const EmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('prefix')
        .setDescription('Thay đổi prefix của bot cho server này')
        .addStringOption(option =>
            option.setName('new_prefix')
                .setDescription('Prefix mới (1-3 ký tự, không chứa khoảng trắng hoặc @)')
                .setRequired(true)
                .setMaxLength(3)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),

    async execute(interaction) {
        try {
            const newPrefix = interaction.options.getString('new_prefix');
            const guildId = interaction.guild?.id;

            // Check if command is used in DM
            if (!guildId) {
                const embed = EmbedBuilder.error(
                    interaction.client,
                    '<PERSON><PERSON><PERSON> nà<PERSON> chỉ có thể sử dụng trong server!'
                );

                return await interaction.reply({ embeds: [embed], ephemeral: true });
            }

            // Check permissions (additional check beyond default permissions)
            const member = interaction.member;
            const isOwner = member.id === config.discord.ownerId;
            const hasManageGuild = member.permissions.has(PermissionFlagsBits.ManageGuild);

            if (!isOwner && !hasManageGuild) {
                const embed = EmbedBuilder.error(
                    interaction.client,
                    'Bạn cần quyền **Manage Server** để sử dụng lệnh này!'
                );

                return await interaction.reply({ embeds: [embed], ephemeral: true });
            }

            // Get current prefix
            const currentPrefix = await prefixManager.getPrefix(guildId);

            // Check if new prefix is the same as current
            if (newPrefix === currentPrefix) {
                const embed = EmbedBuilder.warning(
                    interaction.client,
                    `Prefix hiện tại đã là \`${currentPrefix}\``
                );

                return await interaction.reply({ embeds: [embed], ephemeral: true });
            }

            // Set new prefix
            await prefixManager.setPrefix(guildId, newPrefix);

            // Success response
            const embed = EmbedBuilder.success(
                interaction.client,
                `Prefix đã được thay đổi từ \`${currentPrefix}\` thành \`${newPrefix}\``,
                {
                    fields: [
                        {
                            name: '📝 Hướng dẫn',
                            value: `Từ giờ sử dụng \`${newPrefix}help\` để xem danh sách lệnh`
                        }
                    ],
                    footer: {
                        text: `Thay đổi bởi ${interaction.user.tag}`,
                        iconURL: interaction.user.displayAvatarURL()
                    }
                }
            );

            await interaction.reply({ embeds: [embed] });

            // Log the change
            console.log(`Prefix changed in guild ${interaction.guild.name} (${guildId}) from "${currentPrefix}" to "${newPrefix}" by ${interaction.user.tag}`);

        } catch (error) {
            console.error('Error in prefix command:', error);

            const embed = EmbedBuilder.error(
                interaction.client,
                'Đã xảy ra lỗi khi thay đổi prefix. Vui lòng thử lại sau!'
            );

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [embed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [embed], ephemeral: true });
            }
        }
    },
};
