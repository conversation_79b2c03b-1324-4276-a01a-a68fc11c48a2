require('dotenv').config();

class Config {
    constructor() {
        this.discord = {
            token: process.env.DISCORD_TOKEN,
            clientId: process.env.CLIENT_ID,
            defaultPrefix: process.env.DEFAULT_PREFIX || '!',
            ownerId: process.env.BOT_OWNER_ID
        };

        this.database = {
            path: process.env.DATABASE_PATH || './data/bot.db'
        };
    }

    validate() {
        const required = [
            'discord.token',
            'discord.clientId',
            'discord.ownerId'
        ];

        const missing = [];
        
        for (const key of required) {
            const value = this.getNestedValue(key);
            if (!value) {
                missing.push(key);
            }
        }

        if (missing.length > 0) {
            throw new Error(`Missing required configuration: ${missing.join(', ')}`);
        }

        return true;
    }

    getNestedValue(key) {
        return key.split('.').reduce((obj, k) => obj && obj[k], this);
    }

    get(key) {
        return this.getNestedValue(key);
    }
}

module.exports = new Config();
