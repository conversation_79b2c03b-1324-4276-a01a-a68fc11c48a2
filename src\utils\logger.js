const { EmbedBuilder } = require('discord.js');

class Logger {
    static async sendLog(guild, logType, data) {
        try {
            // Find the log channel
            const logChannel = guild.channels.cache.find(
                channel => channel.name === `${logType}-log` && channel.isTextBased()
            );

            if (!logChannel) {
                console.log(`Log channel ${logType}-log not found in guild ${guild.name}`);
                return false;
            }

            let embed;

            switch (logType) {
                case 'reset':
                    embed = this.createResetLogEmbed(data);
                    break;
                case 'commands':
                    embed = this.createCommandLogEmbed(data);
                    break;
                case 'cash':
                    embed = this.createCashLogEmbed(data);
                    break;
                default:
                    embed = this.createGenericLogEmbed(data);
            }

            await logChannel.send({ embeds: [embed] });
            return true;

        } catch (error) {
            console.error(`Error sending ${logType} log:`, error);
            return false;
        }
    }

    static createResetLogEmbed(data) {
        const embed = new EmbedBuilder()
            .setColor('#FFA500')
            .setAuthor({ 
                name: '🔄 PREFIX RESET', 
                iconURL: data.bot?.displayAvatarURL() 
            })
            .setDescription(`Prefix đã được thay đổi trong server`)
            .addFields(
                { name: '👤 Người thực hiện', value: `${data.user.tag} (${data.user.id})`, inline: true },
                { name: '📝 Prefix cũ', value: `\`${data.oldPrefix}\``, inline: true },
                { name: '📝 Prefix mới', value: `\`${data.newPrefix}\``, inline: true },
                { name: '🏠 Server', value: data.guild.name, inline: true },
                { name: '📅 Thời gian', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
            )
            .setFooter({ 
                text: `Guild ID: ${data.guild.id}`,
                iconURL: data.guild.iconURL() 
            })
            .setTimestamp();

        return embed;
    }

    static createCommandLogEmbed(data) {
        const embed = new EmbedBuilder()
            .setColor('#00BFFF')
            .setAuthor({ 
                name: '⚡ COMMAND USED', 
                iconURL: data.bot?.displayAvatarURL() 
            })
            .setDescription(`Lệnh \`${data.command}\` đã được sử dụng`)
            .addFields(
                { name: '👤 Người dùng', value: `${data.user.tag} (${data.user.id})`, inline: true },
                { name: '📍 Kênh', value: `${data.channel.name}`, inline: true },
                { name: '📅 Thời gian', value: `<t:${Math.floor(Date.now() / 1000)}:R>`, inline: true }
            )
            .setTimestamp();

        if (data.args && data.args.length > 0) {
            embed.addFields({ name: '📋 Tham số', value: data.args.join(', '), inline: false });
        }

        return embed;
    }

    static createCashLogEmbed(data) {
        const embed = new EmbedBuilder()
            .setColor(data.type === 'add' ? '#00FF00' : '#FF0000')
            .setAuthor({ 
                name: data.type === 'add' ? '💰 CASH ADDED' : '💸 CASH REMOVED', 
                iconURL: data.bot?.displayAvatarURL() 
            })
            .setDescription(`Giao dịch tiền tệ`)
            .addFields(
                { name: '👤 Người dùng', value: `${data.user.tag} (${data.user.id})`, inline: true },
                { name: '💵 Số tiền', value: `${data.amount.toLocaleString()} VND`, inline: true },
                { name: '📝 Lý do', value: data.reason || 'Không có lý do', inline: true },
                { name: '📅 Thời gian', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
            )
            .setTimestamp();

        return embed;
    }

    static createGenericLogEmbed(data) {
        const embed = new EmbedBuilder()
            .setColor('#36393F')
            .setAuthor({ 
                name: '📋 SYSTEM LOG', 
                iconURL: data.bot?.displayAvatarURL() 
            })
            .setDescription(data.description || 'System event occurred')
            .setTimestamp();

        if (data.fields) {
            embed.addFields(data.fields);
        }

        return embed;
    }
}

module.exports = Logger;
