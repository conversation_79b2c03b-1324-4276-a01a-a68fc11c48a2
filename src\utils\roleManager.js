const db = require('../database/database');
const config = require('../config/config');

class RoleManager {
    constructor() {
        this.cache = new Map(); // Cache role settings in memory
    }

    async getRoleSettings(guildId) {
        // Check cache first
        if (this.cache.has(guildId)) {
            return this.cache.get(guildId);
        }

        try {
            // Get from database
            const result = await db.get(
                'SELECT owner_role_id, admin_role_id, support_role_id FROM guild_settings WHERE guild_id = ?',
                [guildId]
            );

            const roleSettings = {
                owner: result?.owner_role_id || null,
                admin: result?.admin_role_id || null,
                support: result?.support_role_id || null
            };
            
            // Cache the result
            this.cache.set(guildId, roleSettings);
            
            return roleSettings;
        } catch (error) {
            console.error('Error getting role settings:', error);
            return { owner: null, admin: null, support: null };
        }
    }

    async setRole(guildId, roleType, roleId) {
        if (!guildId) {
            throw new Error('Cannot set role for DM channels');
        }

        if (!['owner', 'admin', 'support'].includes(roleType)) {
            throw new Error('Invalid role type. Must be owner, admin, or support');
        }

        try {
            // First ensure guild_settings record exists
            await db.run(
                `INSERT OR IGNORE INTO guild_settings (guild_id, prefix) VALUES (?, '!')`,
                [guildId]
            );

            // Update the specific role
            const columnName = `${roleType}_role_id`;
            await db.run(
                `UPDATE guild_settings SET ${columnName} = ?, updated_at = CURRENT_TIMESTAMP WHERE guild_id = ?`,
                [roleId, guildId]
            );

            // Update cache
            const currentSettings = await this.getRoleSettings(guildId);
            currentSettings[roleType] = roleId;
            this.cache.set(guildId, currentSettings);

            return true;
        } catch (error) {
            console.error('Error setting role:', error);
            throw error;
        }
    }

    async checkPermission(member, requiredLevel) {
        if (!member || !member.guild) return false;

        const guildId = member.guild.id;
        const userId = member.id;

        // Bot owner always has all permissions
        if (userId === config.discord.ownerId) {
            return true;
        }

        // Get role settings
        const roleSettings = await this.getRoleSettings(guildId);

        // Check based on required level
        switch (requiredLevel) {
            case 'owner':
                return this.hasRole(member, roleSettings.owner);
                
            case 'admin':
                return this.hasRole(member, roleSettings.owner) || 
                       this.hasRole(member, roleSettings.admin);
                       
            case 'support':
                return this.hasRole(member, roleSettings.owner) || 
                       this.hasRole(member, roleSettings.admin) || 
                       this.hasRole(member, roleSettings.support);
                       
            default:
                return false;
        }
    }

    hasRole(member, roleId) {
        if (!roleId) return false;
        return member.roles.cache.has(roleId);
    }

    async getUserLevel(member) {
        if (!member || !member.guild) return null;

        const guildId = member.guild.id;
        const userId = member.id;

        // Bot owner
        if (userId === config.discord.ownerId) {
            return 'bot_owner';
        }

        const roleSettings = await this.getRoleSettings(guildId);

        // Check roles in order of hierarchy
        if (this.hasRole(member, roleSettings.owner)) {
            return 'owner';
        }
        if (this.hasRole(member, roleSettings.admin)) {
            return 'admin';
        }
        if (this.hasRole(member, roleSettings.support)) {
            return 'support';
        }

        return 'user';
    }

    // Clear cache for a specific guild
    clearCache(guildId) {
        this.cache.delete(guildId);
    }

    // Clear entire cache
    clearAllCache() {
        this.cache.clear();
    }
}

module.exports = new RoleManager();
